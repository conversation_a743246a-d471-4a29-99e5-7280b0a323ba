const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const {
  createExam,
  getExams,
  getExamById,
  updateExam,
  deleteExam,
  toggleExamLock,
  startExam,
  submitAnswer,
  completeExam,
  gradeManually,
  triggerAIGrading,
  resetExamQuestions,
  debugExamContent,
  getExamResult,
  regradeExamResult,
  regradeAllExams,
  enableSelectiveAnswering,
  selectQuestion,
  fixExistingResults,
  debugResult,
  comprehensiveAIGrading
} = require('../controllers/examController');
const auth = require('../middleware/auth');
const { isAdmin, isStudent } = require('../middleware/role');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads');

    // Create the directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log('Created uploads directory:', uploadDir);
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const filetypes = /pdf|doc|docx/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);

    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('Only PDF and Word documents are allowed'));
    }
  }
});

// Apply auth middleware to all routes
router.use(auth);

// Admin routes
router.post(
  '/',
  isAdmin,
  upload.fields([
    { name: 'examFile', maxCount: 1 },
    { name: 'answerFile', maxCount: 1 }
  ]),
  createExam
);
router.put(
  '/:id',
  isAdmin,
  upload.fields([
    { name: 'examFile', maxCount: 1 },
    { name: 'answerFile', maxCount: 1 }
  ]),
  updateExam
);
router.delete('/:id', isAdmin, deleteExam);
router.put('/:id/toggle-lock', isAdmin, toggleExamLock);
router.post('/grade/:resultId', isAdmin, gradeManually);
router.post('/ai-grade/:resultId', isAdmin, triggerAIGrading);
router.get('/:id/debug', isAdmin, debugExamContent);
router.post('/:id/reset-questions', isAdmin, resetExamQuestions);

// Debug routes (must come before parameterized routes)
router.get('/test-routes', (req, res) => {
  res.json({
    message: 'Exam routes are working!',
    timestamp: new Date().toISOString(),
    availableRoutes: [
      'POST /:id/start',
      'POST /:id/answer',
      'POST /:id/complete',
      'POST /:id/select-question',
      'GET /result/:id'
    ]
  });
});

// Debug submission issues
router.get('/debug-submission/:examId/:studentId', isAdmin, async (req, res) => {
  try {
    const { examId, studentId } = req.params;
    console.log(`🔍 Debugging submission for exam ${examId}, student ${studentId}`);

    // Find the exam
    const exam = await Exam.findById(examId);
    if (!exam) {
      return res.status(404).json({
        message: 'Exam not found',
        examId
      });
    }

    // Find the result
    const result = await Result.findOne({
      student: studentId,
      exam: examId,
      isCompleted: false
    }).populate({
      path: 'answers.question',
      select: 'text type correctAnswer points section options'
    });

    if (!result) {
      return res.status(404).json({
        message: 'Active exam session not found',
        examId,
        studentId
      });
    }

    // Validate the submission
    const { validateExamSubmission, validateSubmissionTime } = require('../utils/examSubmissionValidator');
    const submissionValidation = validateExamSubmission(result, exam);
    const timeValidation = validateSubmissionTime(result, exam);

    // Analyze answers
    const answerAnalysis = result.answers.map(answer => ({
      questionId: answer.question._id,
      questionType: answer.question.type,
      section: answer.question.section,
      hasTextAnswer: !!(answer.textAnswer && answer.textAnswer.trim()),
      hasSelectedOption: !!(answer.selectedOption && answer.selectedOption.trim()),
      hasMatchingAnswers: !!(answer.matchingAnswers),
      hasOrderingAnswer: !!(answer.orderingAnswer),
      hasDragDropAnswer: !!(answer.dragDropAnswer),
      isSelected: answer.isSelected,
      hasAnyAnswer: !!(
        (answer.textAnswer && answer.textAnswer.trim()) ||
        (answer.selectedOption && answer.selectedOption.trim()) ||
        answer.matchingAnswers ||
        answer.orderingAnswer ||
        answer.dragDropAnswer
      )
    }));

    const answeredQuestions = answerAnalysis.filter(a => a.hasAnyAnswer);
    const selectedQuestions = answerAnalysis.filter(a => a.isSelected !== false);

    res.json({
      message: 'Submission debug information',
      timestamp: new Date().toISOString(),
      exam: {
        id: exam._id,
        title: exam.title,
        allowSelectiveAnswering: exam.allowSelectiveAnswering,
        sectionBRequiredQuestions: exam.sectionBRequiredQuestions,
        sectionCRequiredQuestions: exam.sectionCRequiredQuestions,
        timeLimit: exam.timeLimit
      },
      result: {
        id: result._id,
        isCompleted: result.isCompleted,
        startTime: result.startTime,
        totalAnswers: result.answers.length
      },
      validation: {
        submission: submissionValidation,
        time: timeValidation
      },
      analysis: {
        totalQuestions: answerAnalysis.length,
        answeredQuestions: answeredQuestions.length,
        selectedQuestions: selectedQuestions.length,
        sectionBreakdown: {
          A: answerAnalysis.filter(a => a.section === 'A').length,
          B: answerAnalysis.filter(a => a.section === 'B').length,
          C: answerAnalysis.filter(a => a.section === 'C').length
        },
        answeredBySection: {
          A: answeredQuestions.filter(a => a.section === 'A').length,
          B: answeredQuestions.filter(a => a.section === 'B').length,
          C: answeredQuestions.filter(a => a.section === 'C').length
        },
        selectedBySection: {
          A: selectedQuestions.filter(a => a.section === 'A').length,
          B: selectedQuestions.filter(a => a.section === 'B').length,
          C: selectedQuestions.filter(a => a.section === 'C').length
        }
      },
      answers: answerAnalysis
    });
  } catch (error) {
    console.error('Error debugging submission:', error);
    res.status(500).json({
      message: 'Error debugging submission',
      error: error.message
    });
  }
});

// Test submission functionality for all question types
router.get('/test-submission', isAdmin, async (req, res) => {
  try {
    console.log('🧪 Testing submission functionality for all question types...');

    // Test data for different question types
    const testResults = {
      multipleChoice: { status: 'pending', message: 'Testing multiple choice submission...' },
      trueFalse: { status: 'pending', message: 'Testing true/false submission...' },
      fillInBlank: { status: 'pending', message: 'Testing fill-in-blank submission...' },
      essay: { status: 'pending', message: 'Testing essay submission...' },
      matching: { status: 'pending', message: 'Testing matching submission...' },
      ordering: { status: 'pending', message: 'Testing ordering submission...' },
      dragDrop: { status: 'pending', message: 'Testing drag-drop submission...' }
    };

    // Test validation functions
    const { validateAnswerSubmission, sanitizeAnswerData } = require('../utils/examSubmissionValidator');

    // Test multiple choice
    try {
      const mcData = {
        questionId: 'test-mc-1',
        selectedOption: 'A. Wide Area Network',
        questionType: 'multiple-choice'
      };
      const mcQuestion = {
        _id: 'test-mc-1',
        type: 'multiple-choice',
        text: 'What does WAN stand for?',
        options: [
          { letter: 'A', text: 'Wide Area Network', isCorrect: true },
          { letter: 'B', text: 'Wireless Access Network', isCorrect: false }
        ]
      };

      const mcValidation = validateAnswerSubmission(mcData, mcQuestion);
      const mcSanitized = sanitizeAnswerData(mcData);

      testResults.multipleChoice = {
        status: mcValidation.success ? 'passed' : 'failed',
        validation: mcValidation,
        sanitized: mcSanitized,
        message: mcValidation.success ? 'Multiple choice submission works correctly' : 'Multiple choice validation failed'
      };
    } catch (error) {
      testResults.multipleChoice = {
        status: 'error',
        message: `Multiple choice test error: ${error.message}`
      };
    }

    // Test fill-in-blank
    try {
      const fibData = {
        questionId: 'test-fib-1',
        textAnswer: 'HTTPS',
        questionType: 'fill-in-blank'
      };
      const fibQuestion = {
        _id: 'test-fib-1',
        type: 'fill-in-blank',
        text: 'The _______ protocol is used for secure web communication.',
        correctAnswer: 'HTTPS'
      };

      const fibValidation = validateAnswerSubmission(fibData, fibQuestion);
      const fibSanitized = sanitizeAnswerData(fibData);

      testResults.fillInBlank = {
        status: fibValidation.success ? 'passed' : 'failed',
        validation: fibValidation,
        sanitized: fibSanitized,
        message: fibValidation.success ? 'Fill-in-blank submission works correctly' : 'Fill-in-blank validation failed'
      };
    } catch (error) {
      testResults.fillInBlank = {
        status: 'error',
        message: `Fill-in-blank test error: ${error.message}`
      };
    }

    // Test essay
    try {
      const essayData = {
        questionId: 'test-essay-1',
        textAnswer: 'LAN (Local Area Network) covers small areas like buildings. WAN (Wide Area Network) covers large geographic areas. MAN (Metropolitan Area Network) covers city-sized areas.',
        questionType: 'essay'
      };
      const essayQuestion = {
        _id: 'test-essay-1',
        type: 'essay',
        text: 'Explain the differences between LAN, WAN, and MAN networks.',
        section: 'C',
        points: 10
      };

      const essayValidation = validateAnswerSubmission(essayData, essayQuestion);
      const essaySanitized = sanitizeAnswerData(essayData);

      testResults.essay = {
        status: essayValidation.success ? 'passed' : 'failed',
        validation: essayValidation,
        sanitized: essaySanitized,
        message: essayValidation.success ? 'Essay submission works correctly' : 'Essay validation failed'
      };
    } catch (error) {
      testResults.essay = {
        status: 'error',
        message: `Essay test error: ${error.message}`
      };
    }

    // Calculate overall results
    const totalTests = Object.keys(testResults).length;
    const passedTests = Object.values(testResults).filter(result => result.status === 'passed').length;
    const failedTests = Object.values(testResults).filter(result => result.status === 'failed').length;
    const errorTests = Object.values(testResults).filter(result => result.status === 'error').length;

    res.json({
      message: 'Submission functionality test completed',
      timestamp: new Date().toISOString(),
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        errors: errorTests,
        successRate: `${((passedTests / totalTests) * 100).toFixed(1)}%`
      },
      results: testResults,
      recommendations: generateSubmissionRecommendations(testResults)
    });
  } catch (error) {
    console.error('Error testing submission functionality:', error);
    res.status(500).json({
      message: 'Error testing submission functionality',
      error: error.message
    });
  }
});

// Helper function to generate recommendations
const generateSubmissionRecommendations = (results) => {
  const recommendations = [];

  const failedTests = Object.entries(results).filter(([_, result]) => result.status === 'failed');
  const errorTests = Object.entries(results).filter(([_, result]) => result.status === 'error');

  if (failedTests.length === 0 && errorTests.length === 0) {
    recommendations.push('✅ All question type submissions are working perfectly!');
    recommendations.push('🎯 The validation system is correctly handling all answer types.');
    recommendations.push('🔒 Answer sanitization is working properly for all question types.');
  } else {
    if (failedTests.length > 0) {
      recommendations.push(`❌ ${failedTests.length} question type(s) failed validation:`);
      failedTests.forEach(([type, result]) => {
        recommendations.push(`  - ${type}: ${result.message}`);
      });
    }

    if (errorTests.length > 0) {
      recommendations.push(`🚨 ${errorTests.length} question type(s) had errors:`);
      errorTests.forEach(([type, result]) => {
        recommendations.push(`  - ${type}: ${result.message}`);
      });
    }

    recommendations.push('🔧 Check the detailed results for specific issues.');
    recommendations.push('📝 Ensure all question types have proper validation rules.');
  }

  return recommendations;
};

// Test question types and grading
router.get('/test-question-types', isAdmin, async (req, res) => {
  try {
    console.log('🧪 Running comprehensive question type tests...');

    const { runQuestionTypeTests } = require('../utils/questionTypeTest');
    const results = await runQuestionTypeTests();

    res.json({
      message: 'Question type tests completed',
      timestamp: new Date().toISOString(),
      results: {
        summary: {
          passed: results.passed,
          failed: results.failed,
          total: results.passed + results.failed,
          successRate: `${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`
        },
        details: results.details
      },
      recommendations: generateTestRecommendations(results)
    });
  } catch (error) {
    console.error('Error running question type tests:', error);
    res.status(500).json({
      message: 'Error running question type tests',
      error: error.message
    });
  }
});

// Helper function to generate recommendations based on test results
const generateTestRecommendations = (results) => {
  const recommendations = [];

  const failedTests = results.details.filter(detail => detail.status === 'failed');

  if (failedTests.length === 0) {
    recommendations.push('✅ All question types are working perfectly!');
    recommendations.push('🎯 The grading system is accurately handling all answer types.');
    recommendations.push('🔒 Answer validation is working correctly for all question types.');
  } else {
    const failedQuestionTypes = [...new Set(failedTests.map(test => test.questionType))];

    if (failedQuestionTypes.includes('multipleChoice')) {
      recommendations.push('🔧 Multiple choice grading may need adjustment for semantic matching.');
    }

    if (failedQuestionTypes.includes('fillInBlank')) {
      recommendations.push('🔧 Fill-in-blank questions may need better semantic equivalence detection.');
    }

    if (failedQuestionTypes.includes('essay') || failedQuestionTypes.includes('shortAnswer')) {
      recommendations.push('🔧 AI grading for text answers may need prompt optimization.');
    }

    if (failedQuestionTypes.includes('matching')) {
      recommendations.push('🔧 Matching question validation may need improvement.');
    }

    recommendations.push(`📊 ${failedTests.length} tests failed out of ${results.details.length} total tests.`);
    recommendations.push('🔍 Check the detailed results for specific issues.');
  }

  return recommendations;
};

// Regrading routes (specific routes before parameterized ones)
router.post('/regrade/:resultId', auth, regradeExamResult); // Allow both students and admins to request regrading
router.post('/regrade-all', isAdmin, regradeAllExams);
router.post('/fix-results', isAdmin, fixExistingResults); // Fix existing results with incorrect scores
router.get('/debug-result/:resultId', isAdmin, debugResult); // Debug specific result
router.post('/comprehensive-ai-grading', isAdmin, comprehensiveAIGrading); // Comprehensive AI grading

// Routes for both admin and students
router.get('/', getExams);

// Student routes (specific routes before parameterized ones)
router.get('/result/:id', auth, getExamResult); // Both students and admins can view results

// Parameterized routes (must come last to avoid conflicts)
router.get('/:id', getExamById);
router.post('/:id/start', isStudent, startExam);
router.post('/:id/answer', isStudent, submitAnswer);
router.post('/:id/complete', isStudent, completeExam);
router.post('/:id/select-question', auth, isStudent, selectQuestion); // Ensure auth middleware is applied
router.post('/:id/enable-selective-answering', isAdmin, enableSelectiveAnswering);

module.exports = router;
